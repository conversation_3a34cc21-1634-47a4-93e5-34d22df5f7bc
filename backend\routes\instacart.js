const express = require('express');
const router = express.Router();
const InstacartAisle = require('../models/InstacartAisle');
const InstacartDepartment = require('../models/InstacartDepartment');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartOrder = require('../models/InstacartOrder');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');

// Get all aisles
router.get('/aisles', async (req, res) => {
  try {
    const aisles = await InstacartAisle.find().sort({ aisle_id: 1 });
    res.json(aisles);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all departments
router.get('/departments', async (req, res) => {
  try {
    const departments = await InstacartDepartment.find().sort({ department_id: 1 });
    res.json(departments);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get products with pagination and filtering
router.get('/products', async (req, res) => {
  try {
    const { page = 1, limit = 50, search, department_id, aisle_id } = req.query;
    const query = {};
    
    if (search) {
      query.$text = { $search: search };
    }
    if (department_id) {
      query.department_id = parseInt(department_id);
    }
    if (aisle_id) {
      query.aisle_id = parseInt(aisle_id);
    }
    
    const products = await InstacartProduct.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ product_id: 1 });
    
    const total = await InstacartProduct.countDocuments(query);
    
    res.json({
      products,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get orders with pagination
router.get('/orders', async (req, res) => {
  try {
    const { page = 1, limit = 50, user_id } = req.query;
    const query = {};
    
    if (user_id) {
      query.user_id = parseInt(user_id);
    }
    
    const orders = await InstacartOrder.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ order_id: 1 });
    
    const total = await InstacartOrder.countDocuments(query);
    
    res.json({
      orders,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get order details with products
router.get('/orders/:orderId', async (req, res) => {
  try {
    const orderId = parseInt(req.params.orderId);
    
    const order = await InstacartOrder.findOne({ order_id: orderId });
    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }
    
    const orderProducts = await InstacartOrderProduct.aggregate([
      { $match: { order_id: orderId } },
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_aisles',
          localField: 'product.aisle_id',
          foreignField: 'aisle_id',
          as: 'aisle'
        }
      },
      { $unwind: '$aisle' },
      {
        $lookup: {
          from: 'instacart_departments',
          localField: 'product.department_id',
          foreignField: 'department_id',
          as: 'department'
        }
      },
      { $unwind: '$department' }
    ]);
    
    res.json({
      order,
      products: orderProducts
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Top products by order frequency
router.get('/analytics/top-products', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    
    const topProducts = await InstacartOrderProduct.aggregate([
      {
        $group: {
          _id: '$product_id',
          order_count: { $sum: 1 },
          reorder_count: { $sum: '$reordered' }
        }
      },
      {
        $lookup: {
          from: 'instacart_products',
          localField: '_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_aisles',
          localField: 'product.aisle_id',
          foreignField: 'aisle_id',
          as: 'aisle'
        }
      },
      { $unwind: '$aisle' },
      {
        $lookup: {
          from: 'instacart_departments',
          localField: 'product.department_id',
          foreignField: 'department_id',
          as: 'department'
        }
      },
      { $unwind: '$department' },
      {
        $project: {
          product_id: '$_id',
          product_name: '$product.product_name',
          aisle: '$aisle.aisle',
          department: '$department.department',
          order_count: 1,
          reorder_count: 1,
          reorder_rate: { $divide: ['$reorder_count', '$order_count'] }
        }
      },
      { $sort: { order_count: -1 } },
      { $limit: parseInt(limit) }
    ]);
    
    res.json(topProducts);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Orders by hour of day
router.get('/analytics/orders-by-hour', async (req, res) => {
  try {
    const ordersByHour = await InstacartOrder.aggregate([
      {
        $group: {
          _id: '$order_hour_of_day',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    res.json(ordersByHour);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Orders by day of week
router.get('/analytics/orders-by-dow', async (req, res) => {
  try {
    const ordersByDow = await InstacartOrder.aggregate([
      {
        $group: {
          _id: '$order_dow',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const result = ordersByDow.map(item => ({
      day_of_week: item._id,
      day_name: dayNames[item._id],
      count: item.count
    }));
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Department popularity
router.get('/analytics/department-stats', async (req, res) => {
  try {
    const departmentStats = await InstacartOrderProduct.aggregate([
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_departments',
          localField: 'product.department_id',
          foreignField: 'department_id',
          as: 'department'
        }
      },
      { $unwind: '$department' },
      {
        $group: {
          _id: '$department.department_id',
          department_name: { $first: '$department.department' },
          total_orders: { $sum: 1 },
          unique_products: { $addToSet: '$product_id' },
          reorder_count: { $sum: '$reordered' }
        }
      },
      {
        $project: {
          department_id: '$_id',
          department_name: 1,
          total_orders: 1,
          unique_products_count: { $size: '$unique_products' },
          reorder_count: 1,
          reorder_rate: { $divide: ['$reorder_count', '$total_orders'] }
        }
      },
      { $sort: { total_orders: -1 } }
    ]);
    
    res.json(departmentStats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Aisle popularity
router.get('/analytics/aisle-stats', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    
    const aisleStats = await InstacartOrderProduct.aggregate([
      {
        $lookup: {
          from: 'instacart_products',
          localField: 'product_id',
          foreignField: 'product_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $lookup: {
          from: 'instacart_aisles',
          localField: 'product.aisle_id',
          foreignField: 'aisle_id',
          as: 'aisle'
        }
      },
      { $unwind: '$aisle' },
      {
        $group: {
          _id: '$aisle.aisle_id',
          aisle_name: { $first: '$aisle.aisle' },
          total_orders: { $sum: 1 },
          unique_products: { $addToSet: '$product_id' },
          reorder_count: { $sum: '$reordered' }
        }
      },
      {
        $project: {
          aisle_id: '$_id',
          aisle_name: 1,
          total_orders: 1,
          unique_products_count: { $size: '$unique_products' },
          reorder_count: 1,
          reorder_rate: { $divide: ['$reorder_count', '$total_orders'] }
        }
      },
      { $sort: { total_orders: -1 } },
      { $limit: parseInt(limit) }
    ]);
    
    res.json(aisleStats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analytics: Dashboard summary
router.get('/analytics/summary', async (req, res) => {
  try {
    const [
      totalProducts,
      totalOrders,
      totalAisles,
      totalDepartments,
      totalOrderProducts
    ] = await Promise.all([
      InstacartProduct.countDocuments(),
      InstacartOrder.countDocuments(),
      InstacartAisle.countDocuments(),
      InstacartDepartment.countDocuments(),
      InstacartOrderProduct.countDocuments()
    ]);
    
    res.json({
      totalProducts,
      totalOrders,
      totalAisles,
      totalDepartments,
      totalOrderProducts
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
