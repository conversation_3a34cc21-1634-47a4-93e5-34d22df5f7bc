const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const DemandForecast = require('../models/DemandForecast');
const SalesVelocity = require('../models/SalesVelocity');
const ReorderSuggestion = require('../models/ReorderSuggestion');
const Product = require('../models/Product');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');

class ProphetForecastingService {
  constructor() {
    this.pythonScriptPath = path.join(__dirname, '../../module2_prophet_forecasting.py');
    this.dataPath = path.join(__dirname, '../../data');
    this.modelsPath = path.join(__dirname, '../../models_output');
    this.forecastHorizonDays = 30;
  }

  /**
   * Generate demand forecasts for all products or specific products
   */
  async generateDemandForecasts(productIds = null, options = {}) {
    try {
      console.log('Starting demand forecast generation...');
      
      // Prepare data for forecasting
      const salesData = await this.prepareSalesDataForForecasting(productIds);
      
      if (salesData.length === 0) {
        throw new Error('No sales data available for forecasting');
      }

      // Save data to CSV for Python script
      const csvPath = await this.saveSalesDataToCsv(salesData);
      
      // Run Prophet forecasting
      const forecastResults = await this.runProphetForecasting(csvPath, options);
      
      // Process and save forecast results
      const savedForecasts = await this.processForecastResults(forecastResults);
      
      console.log(`Generated forecasts for ${savedForecasts.length} products`);
      return savedForecasts;
      
    } catch (error) {
      console.error('Error generating demand forecasts:', error);
      throw error;
    }
  }

  /**
   * Prepare sales data from Instacart dataset for forecasting
   */
  async prepareSalesDataForForecasting(productIds = null) {
    try {
      const matchStage = productIds ? 
        { product_id: { $in: productIds } } : 
        {};

      // Get aggregated sales data from Instacart orders
      const salesData = await InstacartOrderProduct.aggregate([
        {
          $lookup: {
            from: 'instacart_orders',
            localField: 'order_id',
            foreignField: 'order_id',
            as: 'order'
          }
        },
        { $unwind: '$order' },
        {
          $lookup: {
            from: 'instacart_products',
            localField: 'product_id',
            foreignField: 'product_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $match: matchStage
        },
        {
          $addFields: {
            // Create a more realistic date spread using order_dow and order_hour_of_day
            order_date: {
              $dateFromString: {
                dateString: {
                  $concat: [
                    '2017-01-',
                    {
                      $cond: {
                        if: { $lt: [{ $add: ['$order.order_dow', 1] }, 10] },
                        then: { $concat: ['0', { $toString: { $add: ['$order.order_dow', 1] } }] },
                        else: { $toString: { $add: ['$order.order_dow', 1] } }
                      }
                    },
                    'T',
                    {
                      $cond: {
                        if: { $lt: ['$order.order_hour_of_day', 10] },
                        then: { $concat: ['0', { $toString: '$order.order_hour_of_day' }] },
                        else: { $toString: '$order.order_hour_of_day' }
                      }
                    },
                    ':00:00.000Z'
                  ]
                }
              }
            }
          }
        },
        {
          $group: {
            _id: {
              product_id: '$product_id',
              date: {
                $dateToString: {
                  format: '%Y-%m-%d',
                  date: '$order_date'
                }
              }
            },
            product_name: { $first: '$product.product_name' },
            aisle_id: { $first: '$product.aisle_id' },
            department_id: { $first: '$product.department_id' },
            units_sold: { $sum: 1 },
            reorder_count: { $sum: '$reordered' },
            order_count: { $sum: 1 }
          }
        },
        {
          $project: {
            product_id: '$_id.product_id',
            date: '$_id.date',
            product_name: 1,
            aisle_id: 1,
            department_id: 1,
            units_sold: 1,
            reorder_count: 1,
            order_count: 1,
            reorder_rate: { $divide: ['$reorder_count', '$order_count'] }
          }
        },
        { $sort: { product_id: 1, date: 1 } }
      ]);

      return salesData;
    } catch (error) {
      console.error('Error preparing sales data:', error);
      throw error;
    }
  }

  /**
   * Save sales data to CSV file for Python processing
   */
  async saveSalesDataToCsv(salesData) {
    try {
      // Ensure data directory exists
      await fs.mkdir(this.dataPath, { recursive: true });
      
      const csvPath = path.join(this.dataPath, `sales_data_${Date.now()}.csv`);
      
      // Helper function to escape CSV values
      const escapeCsvValue = (value) => {
        if (value === null || value === undefined) return '';
        const str = String(value);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };

      // Convert to CSV format that matches Python script expectations
      // The Python script expects: Date, Product line, Quantity, Sales, Invoice ID
      const csvHeader = 'Date,Product line,Quantity,Sales,Invoice ID\n';
      const csvRows = salesData.map(row => {
        // Convert date format from YYYY-MM-DD to MM/DD/YYYY
        const dateParts = row.date.split('-');
        const formattedDate = `${dateParts[1]}/${dateParts[2]}/${dateParts[0]}`;

        return `${formattedDate},${escapeCsvValue(row.product_name)},${row.units_sold},${row.units_sold * 10},${row.order_count}`;
      }).join('\n');

      const csvContent = csvHeader + csvRows;
      await fs.writeFile(csvPath, csvContent);
      
      console.log(`Sales data saved to: ${csvPath}`);
      return csvPath;
    } catch (error) {
      console.error('Error saving sales data to CSV:', error);
      throw error;
    }
  }

  /**
   * Run Prophet forecasting using Python script
   */
  async runProphetForecasting(csvPath, options = {}) {
    return new Promise((resolve, reject) => {
      const pythonArgs = [
        this.pythonScriptPath,
        '--data_path', csvPath,
        '--forecast_days', (options.forecastDays || this.forecastHorizonDays).toString(),
        '--output_path', this.modelsPath
      ];

      console.log('Running Prophet forecasting with args:', pythonArgs);

      const pythonProcess = spawn('python', pythonArgs);
      
      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
        console.log('Python stdout:', data.toString());
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
        console.error('Python stderr:', data.toString());
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          console.log('Prophet forecasting completed successfully');
          resolve({ stdout, stderr, code });
        } else {
          console.error(`Python process exited with code ${code}`);
          reject(new Error(`Prophet forecasting failed: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        console.error('Error spawning Python process:', error);
        reject(error);
      });
    });
  }

  /**
   * Process forecast results and save to database
   */
  async processForecastResults(forecastResults) {
    try {
      // Ensure models output directory exists
      await fs.mkdir(this.modelsPath, { recursive: true });
      
      // Read forecast results from output files
      const forecastFiles = await fs.readdir(this.modelsPath);
      const jsonFiles = forecastFiles.filter(file => file.endsWith('_forecast.json'));
      
      const savedForecasts = [];
      
      for (const file of jsonFiles) {
        const filePath = path.join(this.modelsPath, file);
        const forecastData = JSON.parse(await fs.readFile(filePath, 'utf8'));
        
        // Save forecast to database
        const savedForecast = await this.saveForecastToDatabase(forecastData);
        savedForecasts.push(savedForecast);
      }
      
      return savedForecasts;
    } catch (error) {
      console.error('Error processing forecast results:', error);
      throw error;
    }
  }

  /**
   * Save individual forecast to database
   */
  async saveForecastToDatabase(forecastData) {
    try {
      // Find corresponding product
      const product = await Product.findOne({ sku: forecastData.product_sku });
      
      if (!product) {
        console.warn(`Product not found for SKU: ${forecastData.product_sku}`);
        return null;
      }

      // Create forecast data points
      const forecastDataPoints = forecastData.forecast.map(point => ({
        date: new Date(point.ds),
        predicted_demand: Math.max(0, Math.round(point.yhat)),
        lower_bound: Math.max(0, Math.round(point.yhat_lower)),
        upper_bound: Math.max(0, Math.round(point.yhat_upper)),
        confidence_interval: 0.95
      }));

      // Create demand forecast document
      const demandForecast = new DemandForecast({
        product_id: product._id,
        instacart_product_id: forecastData.instacart_product_id,
        sku: product.sku,
        product_name: product.name,
        category: product.category,
        aisle: forecastData.aisle,
        department: forecastData.department,
        forecast_horizon_days: this.forecastHorizonDays,
        model_type: 'prophet',
        model_version: '1.0',
        training_data_start: new Date(forecastData.training_start),
        training_data_end: new Date(forecastData.training_end),
        forecast_data: forecastDataPoints,
        model_performance: forecastData.performance || {},
        seasonality_components: forecastData.seasonality || {},
        status: 'active'
      });

      await demandForecast.save();
      console.log(`Saved forecast for product: ${product.name}`);
      
      return demandForecast;
    } catch (error) {
      console.error('Error saving forecast to database:', error);
      throw error;
    }
  }

  /**
   * Generate reorder suggestions based on forecasts
   */
  async generateReorderSuggestions(forecastIds = null) {
    try {
      const query = forecastIds ? 
        { _id: { $in: forecastIds }, status: 'active' } : 
        { status: 'active' };
      
      const forecasts = await DemandForecast.find(query).populate('product_id');
      const suggestions = [];
      
      for (const forecast of forecasts) {
        const suggestion = await this.createReorderSuggestion(forecast);
        if (suggestion) {
          suggestions.push(suggestion);
        }
      }
      
      console.log(`Generated ${suggestions.length} reorder suggestions`);
      return suggestions;
    } catch (error) {
      console.error('Error generating reorder suggestions:', error);
      throw error;
    }
  }

  /**
   * Create individual reorder suggestion
   */
  async createReorderSuggestion(forecast) {
    try {
      const product = forecast.product_id;
      const currentStock = product.quantity;
      
      // Calculate predicted demand for different periods
      const demand7Days = forecast.getAveragePredictedDemand(7) * 7;
      const demand14Days = forecast.getAveragePredictedDemand(14) * 14;
      const demand30Days = forecast.getAveragePredictedDemand(30) * 30;
      
      // Calculate days until stockout
      const dailyDemand = forecast.getAveragePredictedDemand(7);
      const daysUntilStockout = dailyDemand > 0 ? Math.floor(currentStock / dailyDemand) : 999;
      
      // Determine urgency level
      let urgencyLevel = 'Low';
      if (daysUntilStockout <= 1) urgencyLevel = 'Critical';
      else if (daysUntilStockout <= 3) urgencyLevel = 'High';
      else if (daysUntilStockout <= 7) urgencyLevel = 'Normal';
      
      // Calculate stockout risk
      const stockoutRisk = Math.min(100, Math.max(0, 100 - (daysUntilStockout * 10)));
      
      // Calculate suggested reorder quantity
      const leadTime = 7; // Default lead time
      const safetyStock = 3; // Default safety stock days
      const suggestedQuantity = Math.ceil((dailyDemand * (leadTime + safetyStock)) - currentStock);
      
      // Calculate suggested reorder date
      const reorderDate = new Date();
      reorderDate.setDate(reorderDate.getDate() + Math.max(0, daysUntilStockout - leadTime));
      
      const reorderSuggestion = new ReorderSuggestion({
        product_id: product._id,
        demand_forecast_id: forecast._id,
        sku: product.sku,
        product_name: product.name,
        current_stock: currentStock,
        predicted_demand_7_days: Math.round(demand7Days),
        predicted_demand_14_days: Math.round(demand14Days),
        predicted_demand_30_days: Math.round(demand30Days),
        suggested_reorder_quantity: Math.max(0, suggestedQuantity),
        suggested_reorder_date: reorderDate,
        urgency_level: urgencyLevel,
        stockout_risk_percentage: Math.round(stockoutRisk),
        days_until_stockout: daysUntilStockout,
        velocity_trend: 'Stable', // This would be calculated from historical data
        confidence_score: 0.85,
        recommendation_reason: `Based on ${this.forecastHorizonDays}-day demand forecast using Prophet model`,
        supplier_lead_time_days: leadTime,
        safety_stock_days: safetyStock
      });
      
      await reorderSuggestion.save();
      return reorderSuggestion;
    } catch (error) {
      console.error('Error creating reorder suggestion:', error);
      return null;
    }
  }
}

module.exports = ProphetForecastingService;
